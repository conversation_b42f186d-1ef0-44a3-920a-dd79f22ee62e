/* This file was auto-generated by TI PinMux on 2024/8/25 at 19:00:35. */
/* This file should only be used as a reference. Some pins/peripherals, */
/* depending on your use case, may need additional configuration. */

/* Some or all of the pins from the following groups are not used by the device tree
	lpddr4
	usbdebugnc
*/

&main_pmx0 {
	gpio0_pins_default: gpio0-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x0068, PIN_OUTPUT, 7) /* (R21) GPMC0_AD11.GPIO0_26 */
			AM62X_IOPAD(0x006c, PIN_OUTPUT, 7) /* (T22) GPMC0_AD12.GPIO0_27 */
			AM62X_IOPAD(0x0070, PIN_OUTPUT, 7) /* (T24) GPMC0_AD13.GPIO0_28 */
			AM62X_IOPAD(0x0074, PIN_INPUT, 7) /* (U25) GPMC0_AD14.GPIO0_29 */
			AM62X_IOPAD(0x0078, <PERSON><PERSON>_OUTPUT, 7) /* (U24) GPMC0_AD15.GPIO0_30 */
			AM62X_IOPAD(0x0084, PIN_OUTPUT_PULLUP, 7) /* (L23) GPMC0_ADVn_ALE.GPIO0_32 */
			AM62X_IOPAD(0x0094, PIN_OUTPUT_PULLUP, 7) /* (N20) GPMC0_BE1n.GPIO0_36 */
			AM62X_IOPAD(0x0098, PIN_OUTPUT_PULLUP, 7) /* (U23) GPMC0_WAIT0.GPIO0_37 */
			AM62X_IOPAD(0x00a0, PIN_INPUT, 1) /* (K25) GPMC0_WPn.GPIO0_39 */
			AM62X_IOPAD(0x00ac, PIN_OUTPUT_PULLUP, 7) /* (L21) GPMC0_CSn1.GPIO0_42 */
			AM62X_IOPAD(0x00b8, PIN_INPUT, 7) /* (U22) VOUT0_DATA0.GPIO0_45 */
			AM62X_IOPAD(0x00bc, PIN_INPUT, 7) /* (V24) VOUT0_DATA1.GPIO0_46 */
			AM62X_IOPAD(0x00c0, PIN_INPUT, 7) /* (W25) VOUT0_DATA2.GPIO0_47 */
			AM62X_IOPAD(0x00c4, PIN_INPUT, 7) /* (W24) VOUT0_DATA3.GPIO0_48 */
			AM62X_IOPAD(0x00c8, PIN_INPUT, 7) /* (Y25) VOUT0_DATA4.GPIO0_49 */
			AM62X_IOPAD(0x00cc, PIN_INPUT, 7) /* (Y24) VOUT0_DATA5.GPIO0_50 */
			AM62X_IOPAD(0x00d0, PIN_INPUT, 7) /* (Y23) VOUT0_DATA6.GPIO0_51 */
			AM62X_IOPAD(0x00d4, PIN_INPUT, 7) /* (AA25) VOUT0_DATA7.GPIO0_52 */
			AM62X_IOPAD(0x00d8, PIN_INPUT, 7) /* (V21) VOUT0_DATA8.GPIO0_53 */
			AM62X_IOPAD(0x00dc, PIN_INPUT, 7) /* (W21) VOUT0_DATA9.GPIO0_54 */
			AM62X_IOPAD(0x00e0, PIN_INPUT, 7) /* (V20) VOUT0_DATA10.GPIO0_55 */
			AM62X_IOPAD(0x00e4, PIN_OUTPUT, 7) /* (AA23) VOUT0_DATA11.GPIO0_56 */
			AM62X_IOPAD(0x00e8, PIN_OUTPUT, 7) /* (AB25) VOUT0_DATA12.GPIO0_57 */
			AM62X_IOPAD(0x00ec, PIN_OUTPUT, 7) /* (AA24) VOUT0_DATA13.GPIO0_58 */
			AM62X_IOPAD(0x00f0, PIN_OUTPUT, 7) /* (Y22) VOUT0_DATA14.GPIO0_59 */
			AM62X_IOPAD(0x00f4, PIN_INPUT, 7) /* (AA21) VOUT0_DATA15.GPIO0_60 */
			AM62X_IOPAD(0x00f8, PIN_INPUT, 7) /* (AB24) VOUT0_HSYNC.GPIO0_61 */
			AM62X_IOPAD(0x00fc, PIN_OUTPUT_PULLUP, 7) /* (Y20) VOUT0_DE.GPIO0_62 */
			AM62X_IOPAD(0x0100, PIN_OUTPUT_PULLUP, 7) /* (AC25) VOUT0_VSYNC.GPIO0_63 */
			AM62X_IOPAD(0x0104, PIN_OUTPUT, 7) /* (AC24) VOUT0_PCLK.GPIO0_64 */
			AM62X_IOPAD(0x0110, PIN_OUTPUT_PULLUP, 7) /* (C25) MMC2_DAT1.GPIO0_67 */
			AM62X_IOPAD(0x012c, PIN_INPUT_PULLUP, 7) /* (AD19) RGMII1_TX_CTL.GPIO0_73 */
			AM62X_IOPAD(0x0130, PIN_OUTPUT_PULLUP, 7) /* (AE19) RGMII1_TXC.GPIO0_74 */
			AM62X_IOPAD(0x0134, PIN_OUTPUT_PULLUP, 7) /* (AE20) RGMII1_TD0.GPIO0_75 */
			AM62X_IOPAD(0x0138, PIN_OUTPUT_PULLUP, 7) /* (AD20) RGMII1_TD1.GPIO0_76 */
			AM62X_IOPAD(0x013c, PIN_OUTPUT_PULLUP, 7) /* (AE18) RGMII1_TD2.GPIO0_77 */
			AM62X_IOPAD(0x0140, PIN_OUTPUT, 7) /* (AD18) RGMII1_TD3.GPIO0_78 */
			AM62X_IOPAD(0x0144, PIN_OUTPUT, 7) /* (AE17) RGMII1_RX_CTL.GPIO0_79 */
			AM62X_IOPAD(0x0148, PIN_OUTPUT, 7) /* (AD17) RGMII1_RXC.GPIO0_80 */
			AM62X_IOPAD(0x014c, PIN_OUTPUT_PULLUP, 7) /* (AB17) RGMII1_RD0.GPIO0_81 */
			AM62X_IOPAD(0x0150, PIN_OUTPUT_PULLUP, 7) /* (AC17) RGMII1_RD1.GPIO0_82 */
			AM62X_IOPAD(0x0154, PIN_OUTPUT_PULLUP, 7) /* (AB16) RGMII1_RD2.GPIO0_83 */
			AM62X_IOPAD(0x015c, PIN_INPUT, 7) /* (AB22) MDIO0_MDIO.GPIO0_85 */
			AM62X_IOPAD(0x0160, PIN_INPUT, 7) /* (AD24) MDIO0_MDC.GPIO0_86 */
		>;
	};
	gpio1_pins_default: gpio1-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x017c, PIN_INPUT, 7) /* (AD22) RGMII2_RX_CTL.GPIO1_1 */
			AM62X_IOPAD(0x0184, PIN_INPUT, 7) /* (AE23) RGMII2_RD0.GPIO1_3 */
			AM62X_IOPAD(0x0190, PIN_INPUT, 7) /* (AE22) RGMII2_RD3.GPIO1_6 */
			AM62X_IOPAD(0x0194, PIN_INPUT, 7) /* (B19) MCASP0_AXR3.GPIO1_7 */
			AM62X_IOPAD(0x01f4, PIN_OUTPUT, 7) /* (D16) EXTINTn.GPIO1_31 */
			AM62X_IOPAD(0x0210, PIN_OUTPUT_PULLUP, 7) /* (AA1) MMC0_DAT1.GPIO1_38 */
			AM62X_IOPAD(0x0214, PIN_OUTPUT, 7) /* (AA2) MMC0_DAT0.GPIO1_39 */
		>;
	};
	gpmclcd_pins_default: gpmclcd-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x003c, PIN_OUTPUT_PULLUP, 0) /* (M25) GPMC0_AD0 */
			AM62X_IOPAD(0x0040, PIN_OUTPUT_PULLUP, 0) /* (N23) GPMC0_AD1 */
			AM62X_IOPAD(0x0044, PIN_OUTPUT_PULLUP, 0) /* (N24) GPMC0_AD2 */
			AM62X_IOPAD(0x0048, PIN_OUTPUT_PULLUP, 0) /* (N25) GPMC0_AD3 */
			AM62X_IOPAD(0x004c, PIN_OUTPUT_PULLUP, 0) /* (P24) GPMC0_AD4 */
			AM62X_IOPAD(0x0050, PIN_OUTPUT_PULLUP, 0) /* (P22) GPMC0_AD5 */
			AM62X_IOPAD(0x0054, PIN_OUTPUT_PULLUP, 0) /* (P21) GPMC0_AD6 */
			AM62X_IOPAD(0x0058, PIN_OUTPUT_PULLUP, 0) /* (R23) GPMC0_AD7 */
			AM62X_IOPAD(0x00a8, PIN_OUTPUT, 0) /* (M21) GPMC0_CSn0 */
			AM62X_IOPAD(0x0088, PIN_OUTPUT, 0) /* (L24) GPMC0_OEn_REn */
			AM62X_IOPAD(0x008c, PIN_OUTPUT, 0) /* (L25) GPMC0_WEn */
		>;
	};
	codeci2c_pins_default: codeci2c-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x01e0, PIN_INPUT_PULLUP, 0) /* (B16) I2C0_SCL */
			AM62X_IOPAD(0x01e4, PIN_INPUT_PULLUP, 0) /* (A16) I2C0_SDA */
		>;
	};
	mcasp0codec_pins_default: mcasp0codec-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x01b0, PIN_INPUT_PULLUP, 0) /* (A20) MCASP0_ACLKR */
			AM62X_IOPAD(0x01a4, PIN_INPUT_PULLUP, 0) /* (B20) MCASP0_ACLKX */
			AM62X_IOPAD(0x01ac, PIN_INPUT_PULLUP, 0) /* (E19) MCASP0_AFSR */
			AM62X_IOPAD(0x01a8, PIN_INPUT_PULLUP, 0) /* (D20) MCASP0_AFSX */
			AM62X_IOPAD(0x01a0, PIN_INPUT_PULLUP, 0) /* (E18) MCASP0_AXR0 */
			AM62X_IOPAD(0x0198, PIN_INPUT_PULLUP, 0) /* (A19) MCASP0_AXR2 */
		>;
	};
	mcasp1txdac_pins_default: mcasp1txdac-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x0038, PIN_INPUT, 3) /* (E24) OSPI0_CSn3.MCASP1_ACLKR */
			AM62X_IOPAD(0x0024, PIN_INPUT, 2) /* (H25) OSPI0_D6.MCASP1_ACLKX */
			AM62X_IOPAD(0x0034, PIN_INPUT, 3) /* (H21) OSPI0_CSn2.MCASP1_AFSR */
			AM62X_IOPAD(0x0028, PIN_INPUT, 2) /* (J22) OSPI0_D7.MCASP1_AFSX */
			AM62X_IOPAD(0x0020, PIN_INPUT, 2) /* (J25) OSPI0_D5.MCASP1_AXR0 */
			AM62X_IOPAD(0x001c, PIN_INPUT, 2) /* (J23) OSPI0_D4.MCASP1_AXR1 */
		>;
	};
	mcasp2ad9864_pins_default: mcasp2ad9864-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x0170, PIN_INPUT, 2) /* (AA18) RGMII2_TD1.MCASP2_ACLKR */
			AM62X_IOPAD(0x0178, PIN_INPUT, 2) /* (AC20) RGMII2_TD3.MCASP2_ACLKX */
			AM62X_IOPAD(0x0188, PIN_INPUT, 2) /* (AB20) RGMII2_RD1.MCASP2_AFSR */
			AM62X_IOPAD(0x0174, PIN_INPUT, 2) /* (AD21) RGMII2_TD2.MCASP2_AFSX */
			AM62X_IOPAD(0x018c, PIN_INPUT, 2) /* (AC21) RGMII2_RD2.MCASP2_AXR0 */
			AM62X_IOPAD(0x0180, PIN_INPUT, 2) /* (AD23) RGMII2_RXC.MCASP2_AXR1 */
		>;
	};
	mmc1sd_pins_default: mmc1sd-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x023c, PIN_INPUT, 0) /* (A21) MMC1_CMD */
			AM62X_IOPAD(0x0234, PIN_OUTPUT, 0) /* (B22) MMC1_CLK */
			AM62X_IOPAD(0x0230, PIN_INPUT, 0) /* (A22) MMC1_DAT0 */
			AM62X_IOPAD(0x022c, PIN_INPUT_PULLUP, 0) /* (B21) MMC1_DAT1 */
			AM62X_IOPAD(0x0228, PIN_INPUT_PULLUP, 0) /* (C21) MMC1_DAT2 */
			AM62X_IOPAD(0x0224, PIN_INPUT_PULLUP, 0) /* (D22) MMC1_DAT3 */
		>;
	};
	qspi0flash_pins_default: qspi0flash-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x0000, PIN_OUTPUT_PULLUP, 0) /* (H24) OSPI0_CLK */
			AM62X_IOPAD(0x002c, PIN_OUTPUT_PULLUP, 0) /* (F23) OSPI0_CSn0 */
			AM62X_IOPAD(0x0030, PIN_OUTPUT_PULLUP, 0) /* (G21) OSPI0_CSn1 */
			AM62X_IOPAD(0x000c, PIN_INPUT_PULLUP, 0) /* (E25) OSPI0_D0 */
			AM62X_IOPAD(0x0010, PIN_INPUT_PULLUP, 0) /* (G24) OSPI0_D1 */
			AM62X_IOPAD(0x0014, PIN_INPUT_PULLUP, 0) /* (F25) OSPI0_D2 */
			AM62X_IOPAD(0x0018, PIN_INPUT_PULLUP, 0) /* (F24) OSPI0_D3 */
		>;
	};
	nfcicssm0uart0_pins_default: nfcicssm0uart0-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x01d8, PIN_INPUT, 6) /* (C15) MCAN0_TX.PR0_UART0_RXD */
			AM62X_IOPAD(0x01dc, PIN_INPUT, 6) /* (E15) MCAN0_RX.PR0_UART0_TXD */
		>;
	};
	epwm1_pins_default: epwm1-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x019c, PIN_OUTPUT_PULLUP, 6) /* (B18) MCASP0_AXR1.EHRPWM1_A */
		>;
	};
	spi0lcdadc_pins_default: spi0lcdadc-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x0090, PIN_OUTPUT, 7) /* (M24) GPMC0_BE0n_CLE.GPIO0_35 */
			AM62X_IOPAD(0x01bc, PIN_INPUT, 0) /* (A14) SPI0_CLK */
			AM62X_IOPAD(0x01b4, PIN_INPUT, 0) /* (A13) SPI0_CS0 */
			AM62X_IOPAD(0x01b8, PIN_INPUT, 0) /* (C13) SPI0_CS1 */
			AM62X_IOPAD(0x01d0, PIN_INPUT, 1) /* (A15) UART0_CTSn.SPI0_CS2 */
			AM62X_IOPAD(0x01c0, PIN_INPUT, 0) /* (B13) SPI0_D0 */
			AM62X_IOPAD(0x01c4, PIN_INPUT, 0) /* (B14) SPI0_D1 */
		>;
	};
	spi1ad9864_pins_default: spi1ad9864-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x020c, PIN_INPUT_PULLUP, 5) /* (AA3) MMC0_DAT2.SPI1_CLK */
			AM62X_IOPAD(0x0208, PIN_OUTPUT_PULLUP, 5) /* (Y4) MMC0_DAT3.SPI1_CS0 */
			AM62X_IOPAD(0x0218, PIN_OUTPUT_PULLUP, 5) /* (AB1) MMC0_CLK.SPI1_CS1 */
			AM62X_IOPAD(0x0220, PIN_INPUT_PULLUP, 5) /* (Y3) MMC0_CMD.SPI1_CS2 */
			AM62X_IOPAD(0x01f8, PIN_INPUT_PULLUP, 5) /* (AC2) MMC0_DAT7.SPI1_D0 */
			AM62X_IOPAD(0x01fc, PIN_INPUT_PULLUP, 5) /* (AD2) MMC0_DAT6.SPI1_D1 */
		>;
	};
	timeriorf38p4mhz_pins_default: timeriorf38p4mhz-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x01d4, PIN_INPUT, 4) /* (B15) UART0_RTSn.TIMER_IO7 */
		>;
	};
	timerpps_pins_default: timerpps-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x0258, PIN_INPUT, 7) /* (F18) USB1_DRVVBUS.GPIO1_51 */
		>;
	};
	timer19p2mhz_pins_default: timer19p2mhz-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x01ec, PIN_INPUT, 2) /* (A17) I2C1_SDA.TIMER_IO1 */
		>;
	};
	uart5gps_pins_default: uart5gps-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x0108, PIN_INPUT_PULLUP, 3) /* (D24) MMC2_DAT3.UART5_RXD */
			AM62X_IOPAD(0x010c, PIN_OUTPUT_PULLUP, 3) /* (E23) MMC2_DAT2.UART5_TXD */
		>;
	};
	uart0debug_pins_default: uart0debug-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x01c8, PIN_INPUT_PULLUP, 0) /* (D14) UART0_RXD */
			AM62X_IOPAD(0x01cc, PIN_OUTPUT_PULLUP, 0) /* (E14) UART0_TXD */
		>;
	};
	uart4bt_pins_default: uart4bt-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x0124, PIN_INPUT_PULLUP, 3) /* (A23) MMC2_SDCD.UART4_RXD */
			AM62X_IOPAD(0x0128, PIN_OUTPUT_PULLUP, 3) /* (B23) MMC2_SDWP.UART4_TXD */
		>;
	};
	uart6jm_pins_default: uart6jm-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x009c, PIN_INPUT_PULLUP, 3) /* (V25) GPMC0_WAIT1.UART6_RXD */
			AM62X_IOPAD(0x0244, PIN_OUTPUT_PULLUP, 1) /* (C17) MMC1_SDWP.UART6_TXD */
		>;
	};
	// uart1jm_pins_default: uart1jm-default-pins {
	// 	pinctrl-single,pins = <
	// 		AM62X_IOPAD(0x01e8, PIN_INPUT, 1) /* (B17) I2C1_SCL.UART1_RXD */
	// 		AM62X_IOPAD(0x01ec, PIN_OUTPUT, 1) /* (A17) I2C1_SDA.UART1_TXD */
	// 	>;
	// };

	usbdebugnc_pins_default: usbdebugnc-default-pins {
		pinctrl-single,pins = <
			AM62X_IOPAD(0x0254, PIN_OUTPUT, 0) /* (C20) USB0_DRVVBUS */
		>;
	};
};


&mcu_pmx0 {
	jtag_pins_default: jtag-default-pins {
		pinctrl-single,pins = <
			AM62X_MCU_IOPAD(0x0078, PIN_INPUT_PULLUP, 0) /* (E12) EMU0 */
			AM62X_MCU_IOPAD(0x007c, PIN_INPUT_PULLUP, 0) /* (C11) EMU1 */
			AM62X_MCU_IOPAD(0x0064, PIN_INPUT_PULLUP, 0) /* (A10) TCK */
			AM62X_MCU_IOPAD(0x006c, PIN_INPUT_PULLUP, 0) /* (A11) TDI */
			AM62X_MCU_IOPAD(0x0070, PIN_OUTPUT_PULLUP, 0) /* (D12) TDO */
			AM62X_MCU_IOPAD(0x0074, PIN_INPUT_PULLUP, 0) /* (B11) TMS */
			AM62X_MCU_IOPAD(0x0068, PIN_INPUT_PULLDOWN, 0) /* (B10) TRSTn */
		>;
	};
	mcugpio0_pins_default: mcugpio0-default-pins {
		pinctrl-single,pins = <
			AM62X_MCU_IOPAD(0x0044, PIN_OUTPUT, 7) /* (A8) MCU_I2C0_SCL.MCU_GPIO0_17 */
			AM62X_MCU_IOPAD(0x0048, PIN_OUTPUT, 7) /* (D10) MCU_I2C0_SDA.MCU_GPIO0_18 */
			AM62X_MCU_IOPAD(0x004c, PIN_OUTPUT, 7) /* (B9) WKUP_I2C0_SCL.MCU_GPIO0_19 */
			AM62X_MCU_IOPAD(0x0050, PIN_OUTPUT, 7) /* (A9) WKUP_I2C0_SDA.MCU_GPIO0_20 */
			AM62X_MCU_IOPAD(0x0080, PIN_INPUT, 7) /* (B7) PMIC_LPM_EN0.MCU_GPIO0_22 */
			AM62X_MCU_IOPAD(0x0084, PIN_OUTPUT, 7) /* (A12) WKUP_CLKOUT0.MCU_GPIO0_23 */
		>;
	};
	lbuscan0m4f_pins_default: lbuscan0m4f-default-pins {
		pinctrl-single,pins = <
			AM62X_MCU_IOPAD(0x0038, PIN_INPUT, 0) /* (B3) MCU_MCAN0_RX */
			AM62X_MCU_IOPAD(0x0034, PIN_OUTPUT, 0) /* (D6) MCU_MCAN0_TX */
		>;
	};
	lbuscan1m4f_pins_default: lbuscan1m4f-default-pins {
		pinctrl-single,pins = <
			AM62X_MCU_IOPAD(0x0040, PIN_INPUT, 0) /* (D4) MCU_MCAN1_RX */
			AM62X_MCU_IOPAD(0x003c, PIN_OUTPUT, 0) /* (E5) MCU_MCAN1_TX */
		>;
	};
	mcuspi0pll481972310_pins_default: mcuspi0pll481972310-default-pins {
		pinctrl-single,pins = <
			AM62X_MCU_IOPAD(0x0008, PIN_OUTPUT, 0) /* (A7) MCU_SPI0_CLK */
			AM62X_MCU_IOPAD(0x0000, PIN_OUTPUT, 0) /* (E8) MCU_SPI0_CS0 */
			AM62X_MCU_IOPAD(0x0004, PIN_OUTPUT, 0) /* (B8) MCU_SPI0_CS1 */
			AM62X_MCU_IOPAD(0x0024, PIN_OUTPUT, 2) /* (B4) WKUP_UART0_RXD.MCU_SPI0_CS2 */
			AM62X_MCU_IOPAD(0x000c, PIN_INPUT, 0) /* (D9) MCU_SPI0_D0 */
			AM62X_MCU_IOPAD(0x0010, PIN_INPUT, 0) /* (C9) MCU_SPI0_D1 */
		>;
	};
	mcuspi1nc_pins_default: mcuspi1nc-default-pins {
		pinctrl-single,pins = <
			AM62X_MCU_IOPAD(0x0030, PIN_OUTPUT_PULLUP, 3) /* (A4) WKUP_UART0_RTSn.MCU_SPI1_CLK */
			AM62X_MCU_IOPAD(0x002c, PIN_OUTPUT_PULLUP, 3) /* (C6) WKUP_UART0_CTSn.MCU_SPI1_CS0 */
			AM62X_MCU_IOPAD(0x0028, PIN_OUTPUT_PULLUP, 2) /* (C5) WKUP_UART0_TXD.MCU_SPI1_CS2 */
			AM62X_MCU_IOPAD(0x001c, PIN_INPUT_PULLUP, 3) /* (A6) MCU_UART0_CTSn.MCU_SPI1_D0 */
			AM62X_MCU_IOPAD(0x0020, PIN_INPUT_PULLUP, 3) /* (B6) MCU_UART0_RTSn.MCU_SPI1_D1 */
		>;
	};
	mcuuart0nc_pins_default: mcuuart0nc-default-pins {
		pinctrl-single,pins = <
			AM62X_MCU_IOPAD(0x0014, PIN_INPUT_PULLUP, 0) /* (B5) MCU_UART0_RXD */
			AM62X_MCU_IOPAD(0x0018, PIN_OUTPUT_PULLUP, 0) /* (A5) MCU_UART0_TXD */
		>;
	};
};
